{"name": "admin", "version": "6.0.0", "private": true, "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "postinstall": "max setup", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "start": "max dev"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@alita/plugins": "^3.3.11", "@ant-design/charts": "^2.1.1", "@ant-design/icons": "^5.3.6", "@ant-design/pro-components": "^2.7.1", "@craftjs/core": "^0.2.12", "@craftjs/layers": "^0.2.7", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@fullcalendar/core": "^6.1.18", "@fullcalendar/daygrid": "^6.1.18", "@fullcalendar/interaction": "^6.1.18", "@fullcalendar/react": "^6.1.18", "@fullcalendar/timegrid": "^6.1.18", "@radix-ui/react-slot": "^1.2.2", "@types/react-beautiful-dnd": "^13.1.8", "ahooks": "^3.7.11", "antd": "5.16.4", "axios": "^1.7.7", "braft-editor": "^2.3.9", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "dayjs": "^1.11.10", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "js-cookie": "^3.0.5", "jsbarcode": "^3.12.1", "lodash": "^4.17.21", "lz-string": "^1.5.0", "number-precision": "^1.6.0", "nzh": "^1.0.12", "qrcode": "^1.5.3", "qs": "6.12.1", "react": "^18.2.0", "react-activation": "^0.13.2", "react-beautiful-dnd": "^13.1.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-pdf": "^6.2.2", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/classnames": "^2.3.1", "@types/history": "^5.0.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.191", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.15", "@types/react": "^18.3.2", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.6", "@types/react-pdf": "^6.2.0", "@umijs/fabric": "^4.0.1", "@umijs/max": "^4.1.2", "cross-env": "^7.0.3", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}, "engines": {"node": ">=12.0.0"}}