export interface SetSysPropertyRequest {
  /**
   * 门店系统账户id
   */
  accountId?: string;
  /**
   * 门店系统账户名称
   */
  accountName?: string;
  /**
   * 门店系统手机号
   */
  accountPhone?: string;
  /**
   * configKey当configKey为空时，用propDimensions赋值configKey
   */
  configKey?: string;
  /**
   * 一体系账户id
   */
  etcAccountId?: string;
  /**
   * 一体系零售商id
   */
  etcMemberId?: string;
  /**
   * 门店系统零售商id
   */
  memberId?: string;
  /**
   * 设置维度零售商维度、账号维度等
   */
  propDimensions?: 'ACCOUNT' | 'MEMBER' | 'GLOBAL';
  /**
   * 备注
   */
  remark?: string;
  /**
   * sessionId
   */
  sessionId?: string;
  /**
   * 类型
   */
  type?: string;
  /**
   * value
   */
  value?: string;
}
