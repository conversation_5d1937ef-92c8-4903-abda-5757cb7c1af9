import { ADVANCE_ACCOUNT } from '@/components/PaymentForm';
import { PayChannel } from '@/components/PaymentForm/types/PayChannel';
import { PayKind } from '@/components/PaymentForm/types/PayKind';
import PaymentFormModal, { PayValues } from '@/components/PaymentFormModal';
import AuthButton from '@/components/common/AuthButton';
import { KeepAliveTabContext } from '@/layouts/context';
import CustomerDetailDrawerForm from '@/pages/customer/list/components/CustomerDetailDrawerForm';
import { getCstDetail } from '@/pages/customer/list/services';
import { CustomerDetailDrawerFormType } from '@/pages/customer/list/types/CustomerDetailDrawerFormType';
import { CustomerEntity } from '@/pages/customer/list/types/CustomerEntity';
import AuditModal from '@/pages/sales/order/detail/components/AuditModal';
import BPointButton from '@/pages/sales/order/detail/components/BPointButton';
import { allOutbound, confirmOrder, confirmPay } from '@/pages/sales/order/edit/services';
import { ConfirmPayRequest } from '@/pages/sales/order/edit/types/confirm.pay.request';
import { RemarkType } from '@/pages/sales/order/edit/types/update.order.remark.request';
import { deleteOrder, withdrawOrder } from '@/pages/sales/order/list/services';
import { OrderStatus, orderStatusMap } from '@/pages/sales/order/list/types/OrderStatus';
import { PayStatus, payStatusMap } from '@/pages/sales/order/list/types/PayStatus';
import { paymentStatusMap } from '@/pages/sales/order/list/types/PaymentStatus';
import { LowPriceCheckStatus } from '@/pages/sales/order/list/types/low.price.check.status';
import { OrderListItemEntity } from '@/pages/sales/order/list/types/order.list.item.entity';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { RightOutlined } from '@ant-design/icons';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { App, Flex, message, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import Cookies from 'js-cookie';
import { useContext, useEffect, useState } from 'react';
import { PaymentBizType } from '../../../list/types/create.payment.request';

export interface BaseInfoProps {
  orderDetail?: OrderListItemEntity;
  queryDetail: () => void;
}

const BaseInfo = (props: BaseInfoProps) => {
  const { orderDetail, queryDetail } = props;
  const { closeTab } = useContext(KeepAliveTabContext);
  const { modal } = App.useApp();
  const intl = useIntl();
  const [paymentVisible, setPaymentVisible] = useState(false);
  const [cstDetail, setCstDetail] = useState<CustomerEntity>();
  const [showAudit, setShowAudit] = useState(false);
  // 客户详情
  const [detailModalProps, setDetailModalProps] = useState<CustomerDetailDrawerFormType>({
    visible: false,
    recordId: '',
    onCancel: () => {
      setDetailModalProps((preModalProps) => ({
        ...preModalProps,
        visible: false,
        recordId: '',
      }));
    },
    title: intl.formatMessage({ id: 'sales.order.detail.customerDetail' }),
  });

  useEffect(() => {
    if (orderDetail?.orders?.cstId) {
      getCstDetail({ cstId: orderDetail?.orders?.cstId }).then((result) => {
        if (result) {
          // @ts-ignore
          setCstDetail(result);
        }
      });
    }
  }, [orderDetail?.orders?.cstId]);

  /**
   * 撤回
   */
  const handleWithdrawOrder = () => {
    modal.confirm({
      content: intl.formatMessage({ id: 'sales.order.detail.confirmWithdraw' }),
      onOk: () =>
        withdrawOrder(orderDetail?.orderId!).then((result) => {
          if (result) {
            message.success(intl.formatMessage({ id: 'sales.order.detail.withdrawSuccess' }));
            queryDetail();
          }
        }),
    });
  };

  /**
   * 一键出库
   */
  const handleStockOut = () => {
    modal.confirm({
      content: intl.formatMessage({ id: 'sales.order.detail.confirmOneClickOutbound' }),
      onOk: () =>
        allOutbound([orderDetail?.orderId!]).then((result) => {
          if (result) {
            message.success(intl.formatMessage({ id: 'sales.order.detail.outboundSuccess' }));
            queryDetail?.();
          }
        }),
    });
  };

  /**
   * 销售确认
   */
  const handleSalemConfirm = () => {
    modal.confirm({
      content: '确定销售确认吗？',
      onOk: () =>
        confirmOrder({ orderId: orderDetail?.orderId }).then((result) => {
          if (result) {
            message.success('销售确认成功');
            queryDetail?.();
          }
        }),
    });
  };

  /**
   * 确认结算
   */
  const handleUpdatePay = (values: PayValues) => {
    const params = { orderId: orderDetail?.orderId, payKind: values.payKind } as ConfirmPayRequest;
    switch (values.payKind) {
      case PayKind.Credit:
        params.payDetailList = [
          {
            payAmount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
            payChannel: PayChannel.PAYMENT_DAYS,
          },
        ];
        break;
      case PayKind.Cash:
        params.payDetailList = values?.payDetailList?.map((item) => {
          if (item.payeeAcount === ADVANCE_ACCOUNT) {
            return {
              payAmount: item.payAmount,
              payChannel: PayChannel.ADVANCE,
            };
          } else {
            return {
              payeeAcount: item.payeeAcount,
              payAmount: item.payAmount,
              payChannel: PayChannel.CASH,
            };
          }
        });
        break;
    }
    confirmPay(params).then((result) => {
      if (result) {
        message.success(intl.formatMessage({ id: 'sales.order.detail.settlementSuccess' }));
        queryDetail?.();
      }
    });
  };

  /**
   * 查看详情
   * @param id
   */
  const openDetailModal = (id: string) => {
    setDetailModalProps((preModalProps) => ({
      ...preModalProps,
      visible: true,
      recordId: id,
    }));
  };

  const orderStatus = orderDetail?.orderStatus?.orderStatus;
  const payStatus = orderDetail?.orderStatus?.payStatus;
  const paymentStatus = orderDetail?.orderStatus?.paymentStatus;

  return (
    <ProCard className="mb-4">
      <Flex justify="space-between">
        <Space>
          <span className="text-[20px] font-semibold">{orderDetail?.orders?.orderNo}</span>
          <span>
            <Tag color={orderStatusMap[orderStatus!]?.color}>
              {orderStatusMap[orderStatus!]?.text}
            </Tag>
            {![OrderStatus.TRADE_CLOSE, OrderStatus.WAIT_TO_HANDLE].includes(orderStatus!) && (
              <>
                <Tag color={payStatusMap[payStatus!]?.color}>{payStatusMap[payStatus!]?.text}</Tag>
                <Tag color={paymentStatusMap[paymentStatus!]?.color}>
                  {paymentStatusMap[paymentStatus!]?.text}
                </Tag>
              </>
            )}
            {orderDetail?.orders?.orderTagList?.includes(2) && (
              <Tag color={'red'}>{intl.formatMessage({ id: 'sales.order.detail.urgency' })}</Tag>
            )}
          </span>
        </Space>
        <Space>
          {[OrderStatus.WAIT_TO_HANDLE].includes(orderDetail?.orderStatus?.orderStatus!) && (
            <AuthButton
              authority="editOrder"
              type={'primary'}
              ghost={true}
              onClick={() => {
                closeTab();
                history.push(`/sales/order/edit?orderNo=${orderDetail?.orders?.orderNo}`);
              }}
            >
              {intl.formatMessage({ id: 'sales.order.detail.edit' })}
            </AuthButton>
          )}
          {[OrderStatus.WAIT_TO_OUTBOUND].includes(orderDetail?.orderStatus?.orderStatus!) && (
            <AuthButton
              authority="withdrawOrder"
              type={'primary'}
              ghost={true}
              onClick={handleWithdrawOrder}
            >
              {intl.formatMessage({ id: 'sales.order.detail.withdraw' })}
            </AuthButton>
          )}
          {[OrderStatus.WAIT_TO_HANDLE, OrderStatus.WAIT_TO_OUTBOUND].includes(
            orderDetail?.orderStatus?.orderStatus!,
          ) && (
              <AuthButton
                authority="deleteOrder"
                type={'primary'}
                ghost={true}
                onClick={() =>
                  modal.confirm({
                    content: intl.formatMessage({ id: 'sales.order.detail.confirmVoid' }),
                    onOk: () =>
                      deleteOrder({ deleteOrderItemList: [{ orderId: orderDetail?.orderId }] }).then(
                        (result) => {
                          // @ts-ignore
                          if (result?.[0]?.success) {
                            message.success(
                              intl.formatMessage({ id: 'sales.order.detail.voidSuccess' }),
                            );
                            queryDetail();
                          } else {
                            // @ts-ignore
                            message.error(result?.[0]?.returnMsg);
                          }
                        },
                      ),
                  })
                }
              >
                {intl.formatMessage({ id: 'sales.order.detail.void' })}
              </AuthButton>
            )}
          {![OrderStatus.TRADE_CLOSE].includes(orderDetail?.orderStatus?.orderStatus!) && (
            <AuthButton
              authority="orderPrint"
              type={'primary'}
              ghost={true}
              onClick={() =>
                window.open(
                  `/print?printType=${PrintType.salesOrder}&orderNo=${orderDetail?.orders?.orderNo}&sessionId=${Cookies.get('s-session-id')}`,
                )
              }
            >
              {intl.formatMessage({ id: 'sales.order.detail.print' })}
            </AuthButton>
          )}
          {orderDetail?.orderLowPriceReview?.reviewStatus === LowPriceCheckStatus.WAIT_REVIEW && (
            <AuthButton
              authority=""
              type={'primary'}
              onClick={() => {
                setShowAudit(true);
              }}
            >
              {intl.formatMessage({ id: 'sales.order.detail.audit' })}
            </AuthButton>
          )}
          <AuthButton
            authority=""
            type="primary"
            ghost={true}
            onClick={() =>
              history.push(
                `/finance/receive/add?cstId=${orderDetail?.orders?.cstId}&cstName=${orderDetail?.orders?.cstName}`,
              )
            }
          >
            {intl.formatMessage({ id: 'sales.order.detail.receive' })}
          </AuthButton>
          {[OrderStatus.WAIT_TO_OUTBOUND, OrderStatus.OUTBOUND_FINISH].includes(
            orderDetail?.orderStatus?.orderStatus!,
          ) &&
            [PayStatus.WAIT_TO_PAY, PayStatus.PART_PAY].includes(
              orderDetail?.orderStatus?.payStatus!,
            ) && (
              <AuthButton
                authority="orderSettlement"
                type="primary"
                onClick={() => setPaymentVisible(true)}
              >
                {intl.formatMessage({ id: 'sales.order.detail.confirmSettlement' })}
              </AuthButton>
            )}
          {[OrderStatus.WAIT_TO_OUTBOUND, OrderStatus.OUTBOUND_FINISH].includes(
            orderDetail?.orderStatus?.orderStatus!,
          ) &&
            [PayStatus.WAIT_TO_PAY].includes(orderDetail?.orderStatus?.payStatus!) &&
            [1].includes(orderDetail?.orderPayList?.[0]?.payType!) && (
              <BPointButton
                bizType={PaymentBizType.Sale}
                orderDetail={{
                  amount: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
                  businessNo: orderDetail?.orders?.orderNo,
                  storeId: orderDetail?.orders?.storeId,
                  customerId: orderDetail?.orders?.cstId,
                  cstName: orderDetail?.orders?.cstName,
                  saleOrgId: orderDetail?.orders?.saleCompanyId,
                }}
                onSuccess={queryDetail}
              />
            )}

          {[OrderStatus.WAIT_TO_OUTBOUND].includes(orderDetail?.orderStatus?.orderStatus!) && (
            <AuthButton authority="saleReturnInWareHouse" type="primary" onClick={handleStockOut}>
              {intl.formatMessage({ id: 'sales.order.detail.oneClickOutbound' })}
            </AuthButton>
          )}
          {[OrderStatus.OUTBOUND_FINISH].includes(orderDetail?.orderStatus?.orderStatus!) &&
            [PayStatus.ALL_PAY].includes(orderDetail?.orderStatus?.payStatus!) && (
              <AuthButton authority="" type="primary" onClick={handleSalemConfirm}>
                销售确认
              </AuthButton>
            )}
        </Space>
      </Flex>
      <ProDescriptions
        className="mt-5"
        column={4}
        dataSource={{
          cstName: orderDetail?.orders?.cstName,
          orderStatus: orderDetail?.orderStatus?.orderStatusName,
          shouldTotalOrderAmountYuan: orderDetail?.orderPrice?.shouldTotalOrderAmountYuan,
          payKindName: orderDetail?.orderPayList?.[0]?.payTypeName,
          storeName: orderDetail?.orders?.storeName,
          saleCompanyName: orderDetail?.orders?.saleCompanyName,
          belongingStoreName: orderDetail?.orders?.belongingStoreName,
          warehouseName: orderDetail?.orderFixedDistributionList?.[0]?.warehouseName,
          createTime: orderDetail?.orders?.createTime,
          createPerson: orderDetail?.orders?.salesman,
          noteDetail: orderDetail?.orderNoteList?.[0]?.noteDetail,
          estimatedDeliveryTime:
            orderDetail?.orderFixedDistributionList?.[0]?.estimatedDeliveryTime,
          deliveryStateName: orderDetail?.orderStatus?.deliveryStateName,
          currency: orderDetail?.orders?.currency,
          exchangeRate: orderDetail?.orders?.exchangeRate,
          gstExcluded: orderDetail?.orders?.gstExcluded,
          saleRemark: orderDetail?.orderNoteList?.find(
            (item) => item.noteType === RemarkType.StoreToCustomer,
          )?.noteDetail,
          innerRemark: orderDetail?.orderNoteList?.find(
            (item) => item.noteType === RemarkType.StoreToInner,
          )?.noteDetail,
        }}
        columns={[
          {
            title: intl.formatMessage({ id: 'sales.order.detail.customerName' }),
            key: 'cstName',
            dataIndex: 'cstName',
            renderText: (text) => (
              <span className="flex">
                {text}
                <RightOutlined
                  className="cursor-pointer"
                  title={intl.formatMessage({ id: 'sales.order.detail.viewCustomerDetail' })}
                  onClick={() => {
                    openDetailModal(orderDetail?.orders?.cstId!);
                  }}
                />
              </span>
            ),
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.orderStatus' }),
            key: 'orderStatus',
            dataIndex: 'orderStatus',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.orderAmount' }),
            key: 'shouldTotalOrderAmountYuan',
            dataIndex: 'shouldTotalOrderAmountYuan',
            valueType: 'money',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.settlementMethod' }),
            key: 'payKindName',
            dataIndex: 'payKindName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.salesStore' }),
            key: 'storeName',
            dataIndex: 'storeName',
          },
          {
            title: '销售主体',
            key: 'saleCompanyName',
            dataIndex: 'saleCompanyName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.belongingStoreName' }),
            key: 'belongingStoreName',
            dataIndex: 'belongingStoreName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.deliveryWarehouse' }),
            key: 'warehouseName',
            dataIndex: 'warehouseName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.orderTime' }),
            key: 'createTime',
            dataIndex: 'createTime',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.estimatedDeliveryTime' }),
            key: 'estimatedDeliveryTime',
            dataIndex: 'estimatedDeliveryTime',
            renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD') : ''),
          },
          {
            title: '配送状态',
            key: 'deliveryStateName',
            dataIndex: 'deliveryStateName',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.detail.creator' }),
            key: 'createPerson',
            dataIndex: 'createPerson',
          },
          {
            title: intl.formatMessage({ id: 'finance.receive.columns.currency' }),
            key: 'currency',
            dataIndex: 'currency',
          },
          {
            title: intl.formatMessage({ id: 'finance.receive.columns.rate' }),
            key: 'exchangeRate',
            dataIndex: 'exchangeRate',
          },
          {
            title: 'GST Excluded',
            key: 'gstExcluded',
            dataIndex: 'gstExcluded',
            renderText: (text) => (text ? 'Yes' : 'No'),
          },
          {
            title: intl.formatMessage({ id: 'sales.order.edit.saleRemark' }),
            key: 'saleRemark',
            dataIndex: 'saleRemark',
          },
          {
            title: intl.formatMessage({ id: 'sales.order.edit.innerRemark' }),
            key: 'innerRemark',
            dataIndex: 'innerRemark',
          },
        ]}
      />
      {[LowPriceCheckStatus.PASS, LowPriceCheckStatus.NOT_PASS].includes(
        orderDetail?.orderLowPriceReview?.reviewStatus!,
      ) && (
          <div className="bg-[#FDF5E8] rounded-[4px] py-[9px] px-[16px] mt-2 flex gap-5">
            <span>
              {intl.formatMessage({ id: 'sales.order.detail.auditMan' })}：
              {orderDetail?.orderLowPriceReview?.reviewPerson}
            </span>
            <span>
              {intl.formatMessage({ id: 'sales.order.detail.auditDate' })}：
              {orderDetail?.orderLowPriceReview?.reviewTime}
            </span>
            {orderDetail?.orderLowPriceReview?.reviewReason && (
              <span>
                {intl.formatMessage({ id: 'sales.order.detail.auditReason' })}：
                {orderDetail?.orderLowPriceReview?.reviewReason}
              </span>
            )}
          </div>
        )}
      <PaymentFormModal
        visible={paymentVisible}
        onClose={() => setPaymentVisible(false)}
        onSubmit={handleUpdatePay}
        cstDetail={cstDetail}
        orderDetail={orderDetail}
        dataSource={{
          payKind: orderDetail?.orderPayDetailList?.[0]?.payKind,
          payDetailList: orderDetail?.orderPayDetailList?.map((item) => {
            if (item.payChannel === PayChannel.ADVANCE) {
              return {
                payeeAcount: ADVANCE_ACCOUNT,
                payAmount: item.payAmountYuan,
                payChannel: item.payChannel,
              };
            } else {
              return {
                payeeAcount: item.payeeAccount,
                payAmount: item.payAmountYuan,
                payChannel: item.payChannel,
              };
            }
          }),
        }}
      />
      <CustomerDetailDrawerForm {...detailModalProps} />
      <AuditModal
        visible={showAudit}
        onClose={() => setShowAudit(false)}
        orderId={orderDetail?.orderId}
        onChange={() => {
          queryDetail();
        }}
      />
    </ProCard>
  );
};

export default BaseInfo;
