import PaymentModal, { PaymentModalProps } from '@/components/PaymentModal';
import { dropDownOption } from '@/pages/sales/order/detail/types/bpoint.type';
import { createPayment } from '@/pages/sales/order/list/services';
import { PaymentBizType } from '@/pages/sales/order/list/types/create.payment.request';
import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, Space } from 'antd';
import { ButtonProps } from 'antd/es/button';
import { useState } from 'react';

export interface BPointButtonProps {
  bizType: PaymentBizType;
  orderDetail: {
    amount?: number;
    businessNo?: string;
    storeId?: string;
    customerId?: string;
    cstName?: string;
    saleOrgId?: string;
  };
  onSuccess?: () => void;
  buttonProps?: ButtonProps;
}

export default function BPointButton(props: BPointButtonProps) {
  const { orderDetail, onSuccess, bizType, buttonProps = {} } = props;
  // BPoint支付信息
  const [paymentModalProps, setPaymentModalProps] = useState<PaymentModalProps>({
    modalData: {
      open: false,
    },
  });
  const [payLoading, setPayLoading] = useState(false);

  return (
    <>
      <Dropdown
        disabled={payLoading}
        menu={{
          items: dropDownOption,
          onClick: (item) => {
            const payType = Number(item.key);
            setPayLoading(true);
            createPayment({
              payType,
              bizType,
              amount: orderDetail.amount,
              businessNo: orderDetail.businessNo,
              redirectionUrl: `${window.location.origin}/order/detail?orderNo=${orderDetail.businessNo}`,
              storeId: orderDetail.storeId,
              customerId: orderDetail.customerId,
              saleOrgId: orderDetail.saleOrgId,
            })
              .then((res) => {
                if (res) {
                  setPaymentModalProps({
                    modalData: {
                      open: true,
                      onCancel: () => setPaymentModalProps({ modalData: { open: false } }),
                    },
                    // @ts-ignore
                    payData: res,
                    payType,
                    onSuccess: onSuccess,
                    cstName: orderDetail.cstName,
                    bizNo: orderDetail.businessNo,
                    amount: orderDetail.amount,
                    saleOrgId: orderDetail.saleOrgId,
                  });
                }
              })
              .finally(() => {
                setPayLoading(false);
              });
          },
        }}
      >
        <Button type="primary" {...buttonProps} loading={payLoading}>
          <Space>
            BPOINT
            <DownOutlined />
          </Space>
        </Button>
      </Dropdown >
      <PaymentModal {...paymentModalProps} />
    </>
  );
}
