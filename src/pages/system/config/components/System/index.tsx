import LeftTitle from '@/components/LeftTitle';
import { querySysPropertyList, setSysProperty } from '@/services/systerm';
import {
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormInstance,
  ProFormSwitch,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { ProFormUploadSingleCard } from '@/components/ProFormItem/ProFormUpload';

const defaultFormData = {
  externalPurchaseAudit: false,
  vinRule: 'VIN',
  pushRule: [],
  priceRule: true,
  stockRule: false,
};

const System = ({ type }: { type: any }) => {
  const intl = useIntl();
  const formRef = useRef<ProFormInstance>();
  const personnelFormRef = useRef<ProFormInstance>();
  const ecommerceFormRef = useRef<ProFormInstance>();
  const [leaveStartCountDate, setLeaveStartCountDate] = useState<string>();

  const handleValuesChange = (changedValue: any, allValues: any) => {
    if (changedValue.hasOwnProperty('pushRule') && changedValue?.pushRule.length === 0) {
      message.error(intl.formatMessage({ id: 'system.config.system.settingFailedEmpty' }));
      queryFormData();
      return;
    } else if (
      changedValue.hasOwnProperty('pushRule') &&
      changedValue?.pushRule.includes('CUSTOM') &&
      !allValues.priceRule
    ) {
      message.error(intl.formatMessage({ id: 'system.config.system.priceRuleError' }));
      formRef.current?.setFieldsValue({ pushRule: ['TXT'] });
      return;
    } else if (changedValue.hasOwnProperty('priceRule') && !changedValue?.priceRule) {
      formRef.current?.setFieldsValue({ pushRule: ['TXT'] });
    }
    const values = formRef.current?.getFieldsValue();
    setSysProperty({ type, value: values, propDimensions: 'MEMBER' }).then((result) => {
      if (result) {
        message.success(intl.formatMessage({ id: 'system.config.system.saveSuccess' }));
        queryFormData();
      }
    });
  };

  useEffect(() => {
    queryFormData();
  }, []);

  const queryFormData = () => {
    querySysPropertyList({ type, propDimensions: 'MEMBER' }).then((result: any) => {
      if (result?.[0]?.value) {
        formRef.current?.setFieldsValue(JSON.parse(result?.[0].value));
      }
    });
    querySysPropertyList({ type: 'sysLeave', propDimensions: 'MEMBER' }).then((result: any) => {
      if (result?.[0]?.value) {
        const resultObj = JSON.parse(result?.[0].value);
        personnelFormRef.current?.setFieldsValue(resultObj);
        setLeaveStartCountDate(resultObj.sysAccrualStartDate);
      }
    });
    querySysPropertyList({ type: 'ecommerce', propDimensions: 'GLOBAL' }).then((result: any) => {
      if (result?.[0]?.value) {
        const resultObj = JSON.parse(result?.[0].value);
        if (resultObj.ecommerceBackground) {
          resultObj.ecommerceBackground = [{ url: resultObj.ecommerceBackground }];
          ecommerceFormRef.current?.setFieldsValue(resultObj);
        } else {
          ecommerceFormRef.current?.setFieldsValue({ ecommerceBackground: [] });
        }
      }
    });
  };

  return (
    <>
      <ProForm
        submitter={false}
        layout="horizontal"
        onValuesChange={handleValuesChange}
        formRef={formRef}
        initialValues={defaultFormData}
      >
        <Space direction={'vertical'} className="w-full" size={'middle'}>
          <ProCard
            title={<LeftTitle title={intl.formatMessage({ id: 'system.config.system.sale' })} />}
            bordered={true}
          >
            <ProFormSwitch
              fieldProps={{ defaultChecked: true }}
              label={intl.formatMessage({ id: 'system.config.system.checkSaleLowPrice' })}
              name="checkSaleLowPrice"
            />
            <p className="text-gray-400 -mt-[16px]">
              {intl.formatMessage({ id: 'system.config.system.checkSaleLowPriceDesc' })}
            </p>
          </ProCard>

          <ProCard
            title={
              <LeftTitle title={intl.formatMessage({ id: 'system.config.system.purchase' })} />
            }
            bordered={true}
          >
            <ProFormSwitch
              label={intl.formatMessage({ id: 'system.config.system.externalPurchaseAudit' })}
              name="externalPurchaseAudit"
            />
            <p className="text-gray-400 -mt-[16px]">
              {intl.formatMessage({ id: 'system.config.system.externalPurchaseAuditDesc' })}
            </p>
            <ProFormSwitch
              label={intl.formatMessage({ id: 'system.config.system.purchaseInSyncGood' })}
              name="purchaseInSyncGood"
            />
            <p className="text-gray-400 -mt-[16px]">
              {intl.formatMessage({ id: 'system.config.system.purchaseInSyncGoodDesc' })}
            </p>
          </ProCard>
        </Space>
      </ProForm>

      <ProCard
        className="mt-[16px]"
        title={<LeftTitle title={intl.formatMessage({ id: 'system.config.system.personnel' })} />}
        bordered={true}
      >
        <ProForm
          layout="inline"
          formRef={personnelFormRef}
          onFinish={(values) => {
            console.log(values);
            setSysProperty({ type: 'sysLeave', value: values, propDimensions: 'MEMBER' }).then(
              (result) => {
                if (result) {
                  message.success(intl.formatMessage({ id: 'common.message.save.success' }));
                  queryFormData();
                }
              },
            );
          }}
          submitter={{
            render: (props, dom) => (
              <>
                {Boolean(leaveStartCountDate) ? null : (
                  <Button type="link" onClick={() => props.form?.submit?.()}>
                    {intl.formatMessage({ id: 'common.button.save' })}
                  </Button>
                )}
              </>
            ),
          }}
        >
          <ProFormDatePicker
            readonly={Boolean(leaveStartCountDate)}
            name="sysAccrualStartDate"
            label={intl.formatMessage({ id: 'system.config.system.leaveStartCountDate' })}
          />
        </ProForm>
        <p className="text-gray-400 mt-[5px]">
          {intl.formatMessage({ id: 'system.config.system.leaveStartCountDateDesc' })}
        </p>
      </ProCard>
      <ProCard
        className="mt-[16px]"
        title={<LeftTitle title={intl.formatMessage({ id: 'system.config.system.ecommerce' })} />}
        bordered={true}
      >
        <ProForm submitter={false} formRef={ecommerceFormRef} layout="horizontal">
          <ProFormUploadSingleCard
            name="ecommerceBackground"
            label={intl.formatMessage({ id: 'system.config.system.ecommerceBackground' })}
            onChange={(fileList) => {
              if (fileList?.length === 0) {
                setSysProperty({
                  type: 'ecommerce',
                  value: JSON.stringify({}),
                  propDimensions: 'MEMBER',
                }).then((result) => {
                  if (result) {
                    message.success(intl.formatMessage({ id: 'common.message.save.success' }));
                    queryFormData();
                  }
                });
              }
              const url = fileList?.[0]?.response?.data?.[0];
              if (url) {
                setSysProperty({
                  type: 'ecommerce',
                  value: JSON.stringify({ ecommerceBackground: url }),
                  propDimensions: 'GLOBAL',
                }).then((result) => {
                  if (result) {
                    message.success(intl.formatMessage({ id: 'common.message.save.success' }));
                    queryFormData();
                  }
                });
              }
            }}
          />
        </ProForm>
      </ProCard>
    </>
  );
};

export default System;
