import GoodsSearch from '@/components/GoodsSearch';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import PaymentExternalForm from '@/components/PaymentExternalForm';
import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import StocksInfoDrawer from '@/components/StocksInfoDrawer';
import AuthButton from '@/components/common/AuthButton';
import MoneyText from '@/components/common/MoneyText';
import SubTitle from '@/components/common/SubTitle';
import { KeepAliveTabContext } from '@/layouts/context';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { queryStoreByAccount } from '@/pages/personnel/user/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { importData } from '@/utils/importData';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  ActionType,
  EditableProTable,
  PageContainer,
  ProCard,
  ProForm,
  ProFormDatePicker,
  ProFormDependency,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  QueryFilter,
} from '@ant-design/pro-components';
import { history, useAccess, useIntl, useSearchParams } from '@umijs/max';
import { useAsyncEffect, useDebounceFn } from 'ahooks';
import {
  Button,
  Checkbox,
  ConfigProvider,
  Flex,
  Popconfirm,
  Space,
  Spin,
  Tag,
  message,
} from 'antd';
import { isEmpty } from 'lodash';
import { useContext, useEffect, useRef, useState } from 'react';
import { queryPurchaseLinePagePost, queryPurchaseOrderDetailPost } from '../detail/services';
import type { LinePostEntity } from '../detail/types/line.post.entity';
import type { PurchasePostEntity } from '../detail/types/purchase.post.entity';
import { orderStatusOptions } from '../list/types/OrderStatus';
import { PayTypeStatus } from '../list/types/PayTypeStatus';
import SupplierDetailDrawer from '../supplier/components/SupplierDetailDrawer';
import { YesNoStatus } from '../supplier/operation/types/YesNo';
import { AddPostEntity } from '../supplier/operation/types/add.post.entity';
import { queryFullById, querySupplierList } from '../supplier/services';
import { PostListTableColumns } from './config/postListTableColumns';
import {
  addPurchaseLineBatchPost,
  confirmPayExternalPurchasePost,
  createExternalPurchasePost,
  deletePurchaseLinePost,
  inStockExternalPurchasePost,
  submitExternalPurchasePost,
  updateExternalPurchasePost,
  updatePurchaseLinePost,
} from './services';
import type { AddItemExternalRequest } from './types/add.item.external.request';
import type { UpdateExternalRequest } from './types/update.external.request';

const PurchaseList = () => {
  const intl = useIntl();
  const [searchParams, setSearchParams] = useSearchParams();
  const orderId = searchParams.get('purchaseOrderId') ?? '';
  const [orderNo, setOrderNo] = useState<string | undefined>('');
  const [detailData, setDetailData] = useState<PurchasePostEntity>({});
  const [supplierDetail, setSupplierDetail] = useState<AddPostEntity>({});
  const isMultiCurrency = Boolean(supplierDetail.supplierSettleInfo?.isMultiCurrency);
  const [supplierDetailDrawer, setSupplierDetailDrawer] = useState<any>({
    open: false,
    supplierId: undefined,
  });
  const formRef = useRef<ProFormInstance>();
  const formRefOne = useRef<ProFormInstance>();
  const [accountList, setAccountList] = useState<any[]>([]);
  const access = useAccess();
  const [loading, setLoading] = useState<boolean>(false);

  const receiveWarehouseId = ProForm?.useWatch('receiveWarehouseId', formRef.current ?? {});
  const supplierId = ProForm?.useWatch('supplierId', formRef.current ?? {});
  const storeId = ProForm?.useWatch('storeId', formRef.current ?? {});
  const plainOptions: any[] = [];

  const auditPlainOptions: any[] = [];

  if (access.hasButtonPerms('externalConfirmSettlement')) {
    plainOptions.push({
      label: intl.formatMessage({ id: 'purchase.external.button.confirmSettlement' }),
      value: '0',
    });
  }
  if (access.hasButtonPerms('externalInput')) {
    plainOptions.push({
      label: intl.formatMessage({ id: 'purchase.external.button.oneClickInStock' }),
      value: '1',
    });
  }
  if (access.hasButtonPerms('externalSubmitToPrint')) {
    plainOptions.push({
      label: intl.formatMessage({ id: 'purchase.external.button.submitAndPrint' }),
      value: '2',
    });
    auditPlainOptions.push({
      label: intl.formatMessage({ id: 'purchase.external.button.submitAndPrint' }),
      value: '2',
    });
  }

  useAsyncEffect(async () => {
    if (!isEmpty(orderId)) {
      queryDetail();
    } else {
      //新增
      setOrderNo(''); //清空数据
      setDetailData({});
      formRef.current?.resetFields();
      formRefOne.current?.resetFields();
      setDataSourceCache([]);
    }
  }, [orderId]);

  useEffect(() => {
    if (supplierId) {
      queryFullById({ id: supplierId }).then((res) => {
        setSupplierDetail(res ?? {});
      });
    }
  }, [supplierId]);

  const queryDetail = async () => {
    const data = await queryPurchaseOrderDetailPost({ id: orderId });
    if (data) {
      setOrderNo(data.orderNo);
      formRef.current?.setFieldsValue(data);
      setDetailData(data);
      formRefOne.current?.setFieldsValue(data);
      if (data?.paySubTypeList?.length == 0) {
        formRefOne.current?.setFieldValue('paySubTypeList', undefined);
      }
    }
  };
  const onRefresh = async () => {
    queryDetail();
    actionRef.current?.reload(true);
  };

  const actionRef = useRef<ActionType>();

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>(() => []);

  const { closeTab } = useContext(KeepAliveTabContext);
  // 显示库存信息
  const [stocksDrawer, setStocksDrawer] = useState<StocksInfoDrawerProps>({
    visible: false,
  });

  useEffect(() => {
    if (detailData?.storeId) {
      queryMemberAccountPage({
        belongToStore: [detailData?.storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
  }, [detailData?.storeId]);
  /**
   * 编辑 头部信息
   * @param item
   */
  const handleUpdateExternal = async (item: UpdateExternalRequest) => {
    const data = await updateExternalPurchasePost({ ...item, id: orderId });
    if (data) {
      queryDetail();
    }
  };

  const clikSubmit = async () => {
    const data = await submitExternalPurchasePost({ orderNo });
    if (data) {
      if (checkedList.length > 0) {
        if (checkedList.includes('0')) {
          //确认结算
          const payValues = await formRefOne.current?.validateFields();
          const data = await confirmPayExternalPurchasePost({ orderNo, ...payValues });
          if (data && checkedList.includes('1')) {
            //一键入库
            const data = await inStockExternalPurchasePost({ orderNo });
          }
        }
        if (checkedList.includes('1') && !checkedList.includes('0')) {
          //单独存在 一键入库
          const data = await inStockExternalPurchasePost({ orderNo });
        }
        if (checkedList.includes('2')) {
          window.open(`/print?purchaseId=${orderId}&printType=${PrintType.purchaseOrder}`);
        }
      }
      closeTab();
      history.push('/purchase/detail?purchaseId=' + orderId + '&purchaseOrderNo=' + orderNo);
    }
  };
  const rules = [{ required: true }];
  const layout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 },
  };

  const [checkedList, setCheckedList] = useState<string[]>([]);
  const onCheckboxChange = (list: string[]) => {
    setCheckedList(list);
  };

  const [dataSourceCache, setDataSourceCache] = useState<LinePostEntity[]>([]);

  const handleCreate = async (itemList: any[]) => {
    // const item = itemList[0];
    return formRef.current
      ?.validateFields?.()
      .then(async (values) => {
        const { orderNo, id } = await createExternalPurchasePost(values);
        if (!isEmpty(orderNo)) {
          const lineList = itemList.map((item) => {
            return {
              ...item,
              num: item.number,
            };
          });
          const data = await addPurchaseLineBatchPost({ orderNo, lineList });
          if (data) {
            history.replace(`/purchase/external?purchaseOrderId=${id}`);
          }
          return data;
        }
      })
      .catch((e) => {
        if (e?.errorFields?.length > 0) {
          message.warning(
            intl.formatMessage({ id: 'purchase.external.message.completeRequiredInfo' }),
          );
        }
        return false;
      });
  };

  const handleDelete = (key: any) => {
    return deletePurchaseLinePost({ ids: [key], orderNo }).then((result) => {
      if (result) {
        message.success(intl.formatMessage({ id: 'purchase.external.message.deleteSuccess' }));
        onRefresh();
      }
    });
  };

  const handleAddItem = async (itemList: any[]) => {
    const lineList = itemList.map((item) => {
      return {
        ...item,
        num: item.number,
      };
    });
    const data = await addPurchaseLineBatchPost({ orderNo, lineList });
    if (data) {
      message.success(intl.formatMessage({ id: 'purchase.external.message.addSuccess' }));
      //刷新列表
      onRefresh();
    }
    return data;
  };
  const handleUpdateItem = async (item: AddItemExternalRequest) => {
    const data = await updatePurchaseLinePost({ ...item, orderNo });
    if (data) {
      //刷新列表
      onRefresh();
    }
  };

  // onAdd防抖
  const { run: updaterun } = useDebounceFn(
    (data) => {
      handleUpdateItem(data);
    },
    { wait: 300 },
  );

  const { run: updateExternalPurchase } = useDebounceFn(
    (data) => {
      if (!orderId) return;
      handleUpdateExternal(data);
    },
    { wait: 300 },
  );

  const handleUpdatePayType = async (value: any, values: any) => {
    if (PayTypeStatus.HANGING_ACCOUNTS == values?.payType) {
      //挂账直接保存
      handleUpdateExternal(values);
    } else {
      if (values?.paySubTypeList == undefined || values?.paySubTypeList?.length == 0) {
        //直接塞默认值
        handleUpdateExternal({
          payType: PayTypeStatus.CASH,
          paySubTypeList: [{ id: accountList?.[0]?.value, amount: detailData?.sumAmount + '' }],
        });
        return;
      }
      // 手动触发校验
      const newTimeout = window.setTimeout(async () => {
        try {
          const validatedFields = await formRefOne.current?.validateFields();
          if (validatedFields) {
            handleUpdateExternal(validatedFields);
          }
        } catch (error) {
          // 有字段未通过校验
        }
      }, 500);
    }
  };
  /**
   * 修改仓库
   * @param e
   */
  const externalUpdateOutWarehouse = async (e: string) => {
    if (!isEmpty(orderId)) {
      await handleUpdateExternal({ receiveWarehouseId: e });
    }
  };

  const { run } = useDebounceFn(
    (changedValues: any, allValues: any) => handleUpdatePayType(changedValues, allValues),
    {
      wait: 500,
    },
  );

  const importFn = (orderNo: string) => {
    importData({
      moduleId: 'BATCH_IMPORT_PURCHASE_LINE',
      systemId: 'GRIPX_STORE_SYS',
      downloadFileName:
        'gie/static/etc-saas/%E4%B8%80%E4%BD%93%E7%B3%BB%E9%9B%B6%E5%94%AE%E9%97%A8%E5%BA%97-%E9%87%87%E8%B4%AD%E6%98%8E%E7%BB%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx',
      taskDesc: intl.formatMessage({ id: 'purchase.external.import.taskDesc' }),
      params: { orderNo },
      onSuccess: () => {
        onRefresh();
      },
    });
  };

  // 导入
  const handleImport = () => {
    if (orderNo) {
      importFn(orderNo);
    } else {
      formRef.current?.validateFields?.().then(async (values) => {
        const { orderNo, id } = await createExternalPurchasePost(values);
        if (orderNo) {
          history.push(`/purchase/external?purchaseOrderId=${id}`);
          importFn(orderNo);
        }
      });
    }
  };

  /**
   * 查看库存信息面板
   */
  const handleViewStocksInfo = (data: StocksInfoDrawerProps) => {
    setStocksDrawer({
      ...data,
      visible: true,
      warehouseId: receiveWarehouseId,
    });
  };

  return (
    <PageContainer>
      <Spin spinning={loading}>
        <ProCard bodyStyle={{ padding: 0 }}>
          <QueryFilter layout="vertical" submitter={false} formRef={formRef} labelWidth={100}>
            <ProFormSelect
              name="supplierId"
              label={intl.formatMessage({ id: 'purchase.external.label.supplier' })}
              placeholder={intl.formatMessage({
                id: 'purchase.external.placeholder.selectSupplier',
              })}
              rules={rules}
              width={'md'}
              disabled={!isEmpty(orderNo)}
              fieldProps={{ fieldNames: { label: 'supplierName', value: 'supplierId' } }}
              request={querySupplierList}
            />
            <ProFormSelect
              name="storeId"
              label={intl.formatMessage({ id: 'purchase.external.label.purchaseStore' })}
              width={'md'}
              disabled={!isEmpty(orderNo)}
              rules={rules}
              placeholder={intl.formatMessage({
                id: 'purchase.external.placeholder.selectPurchaseStore',
              })}
              debounceTime={300}
              fieldProps={{ fieldNames: { label: 'name', value: 'id' } }}
              request={() =>
                queryStoreByAccount({ status: 1 }).then((s) => {
                  if (s[0] && isEmpty(searchParams.get('purchaseOrderId'))) {
                    formRef.current?.setFieldValue('storeId', s[0].id);
                  }
                  return s;
                })
              }
            />
            <ProFormDependency name={['storeId']}>
              {({ storeId }) => {
                return (
                  <ProFormSelect
                    label={intl.formatMessage({ id: 'purchase.external.label.receiveWarehouse' })}
                    rules={rules}
                    //disabled={!isEmpty(orderNo)}
                    name="receiveWarehouseId"
                    width={'md'}
                    fieldProps={{
                      fieldNames: { label: 'warehouseName', value: 'id' },
                    }}
                    params={{ storeIdList: [storeId] }}
                    onChange={externalUpdateOutWarehouse}
                    request={(params) => {
                      if (params?.storeIdList.length > 0) {
                        return warehouseList({ ...params, state: YesNoStatus.YES }).then((s) => {
                          if (
                            s?.warehouseSimpleRoList?.length! > 0 &&
                            isEmpty(searchParams.get('purchaseOrderId'))
                          ) {
                            formRef.current?.setFieldValue(
                              'receiveWarehouseId',
                              s?.warehouseSimpleRoList?.[0].id,
                            );
                          }
                          if (s?.warehouseSimpleRoList?.length == 0) {
                            //无仓库信息
                            formRef.current?.setFieldValue('receiveWarehouseId', undefined);
                          }
                          return s.warehouseSimpleRoList ?? [];
                        });
                      }
                      return new Promise((resolve, reject) => {
                        return reject([]);
                      });
                    }}
                  />
                );
              }}
            </ProFormDependency>
            {isMultiCurrency && (
              <ProFormCurrency
                fieldsName={{
                  currency: 'currency',
                  rate: 'exchangeRate',
                }}
                onChange={updateExternalPurchase}
              />
            )}
            <ProFormText
              name="sourceNo"
              label="Reference No."
              fieldProps={{
                onChange: (e) =>
                  updateExternalPurchase({
                    sourceNo: e.target.value,
                  }),
              }}
            />
          </QueryFilter>
          {supplierId && (
            <div className="p-4 mt-[-25px]">
              <div className="bg-gray-100 p-4 rounded">
                <Flex vertical>
                  <Flex justify="space-between">
                    <Space>
                      <span className="font-semibold text-lg">
                        {supplierDetail?.supplierInfo?.supplierName}
                      </span>
                      {supplierDetail?.supplierInfo?.country && (
                        <Tag color="blue">{supplierDetail?.supplierInfo?.country}</Tag>
                      )}
                      {supplierDetail?.supplierSettleInfo?.settleType && (
                        <Tag color="orange">{supplierDetail?.supplierSettleInfo?.settleType}</Tag>
                      )}
                    </Space>
                    <Button
                      type="link"
                      onClick={() =>
                        setSupplierDetailDrawer({ open: true, supplierId: supplierId })
                      }
                    >
                      {intl.formatMessage({ id: 'purchase.external.button.viewDetails' })}
                    </Button>
                  </Flex>
                  <Flex gap={30}>
                    <span>
                      {intl.formatMessage({ id: 'purchase.supplier.list.columns.supplierCode' })}:{' '}
                      {supplierDetail?.supplierInfo?.supplierCode}
                    </span>
                    <span>ABN: {supplierDetail?.supplierInfo?.abn}</span>
                    <span>
                      GST Excluded: {supplierDetail?.supplierSettleInfo?.gstExcluded ? 'Yes' : 'No'}
                    </span>
                    <span>
                      {intl.formatMessage({ id: 'purchase.external.label.supplierPayable' })}:{' '}
                      {supplierDetail?.supplierSettleInfo?.currencyPayableAmount}
                    </span>
                  </Flex>
                </Flex>
              </div>
            </div>
          )}
        </ProCard>
        <ProCard bodyStyle={{ paddingTop: 24 }} className="mt-4">
          <GoodsSearch
            supplierId={supplierId}
            bizType={GoodsSearchBizType.ExternalPurchase}
            addedItemSns={dataSourceCache?.map((item) => item.itemSn ?? '')}
            onAdd={async (itemList) => {
              setLoading(true);
              let result = false;
              if (isEmpty(orderNo)) {
                result = await handleCreate(itemList);
              } else {
                result = await handleAddItem(itemList);
              }
              if (!result) {
                setLoading(false);
              }
            }}
            storeId={storeId}
            warehouseId={receiveWarehouseId}
          />
        </ProCard>
        <ProCard
          className="mt-4"
          bodyStyle={{ padding: 0 }}
          title={
            <Space>
              <SubTitle
                text={intl.formatMessage({ id: 'purchase.external.subtitle.purchaseOrderDetail' })}
              />
              <span className="text-[14px] text-black/[0.8]">
                {intl.formatMessage({ id: 'purchase.external.label.purchaseOrderNo' })}：
                {detailData?.orderNo ?? '-'}
              </span>
              <span className="text-[14px] text-black/[0.8]">
                {intl.formatMessage({ id: 'purchase.external.label.purchaseStatus' })}：
                {orderStatusOptions[detailData?.orderStatus!]?.text ?? '-'}
              </span>
            </Space>
          }
        >
          <EditableProTable<LinePostEntity, any>
            rowKey="id"
            search={false}
            pagination={false}
            options={{
              setting: true,
              density: false,
              reload: false,
            }}
            scroll={{ x: 'max-content' }}
            recordCreatorProps={false}
            actionRef={actionRef}
            columns={PostListTableColumns({ handleUpdateItem: updaterun, handleViewStocksInfo })}
            editable={{
              type: 'multiple',
              editableKeys,
              onChange: setEditableRowKeys,
              onDelete: handleDelete,
              actionRender: (row, config, defaultDoms) => {
                return [
                  <Popconfirm
                    key="delete"
                    title={intl.formatMessage({ id: 'purchase.external.confirm.delete' })}
                    onConfirm={() => handleDelete(row.id)}
                  >
                    <a key="externalItemDelete" type="link">
                      {intl.formatMessage({ id: 'purchase.external.button.delete' })}
                    </a>
                  </Popconfirm>,
                ];
              },
            }}
            params={{ orderNo }}
            request={async (params) => {
              if (!isEmpty(orderNo)) {
                setLoading(true);
                const data = await queryPurchaseLinePagePost(params);
                setDataSourceCache(data?.data);
                if (data?.data) {
                  setEditableRowKeys(data?.data.map((s) => s.id!));
                }
                setLoading(false);
                return data;
              }
              return [];
            }}
            headerTitle={
              <Space>
                <AuthButton
                  type="primary"
                  ghost
                  key="externalImport"
                  authority="externalImport"
                  onClick={handleImport}
                >
                  {intl.formatMessage({ id: 'purchase.external.button.batchImport' })}
                </AuthButton>
              </Space>
            }
          />
          <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
            <ProForm
              formRef={formRefOne}
              submitter={false}
              disabled={isEmpty(orderId)}
              onValuesChange={run}
            >
              <Flex justify="space-between" className="px-6">
                <PaymentExternalForm storeId={detailData?.storeId} />
                <ProFormDigit
                  label={
                    <text className="font-semibold">
                      {intl.formatMessage({ id: 'purchase.external.label.freight' })}
                    </text>
                  }
                  name="freightAmount"
                  fieldProps={{
                    addonBefore: '$',
                  }}
                />
                <ProFormDatePicker
                  label={
                    <text className="font-semibold">
                      {intl.formatMessage({ id: 'purchase.external.label.deliveryTime' })}
                    </text>
                  }
                  name="deliveryTime"
                  fieldProps={{
                    showTime: true,
                    format: 'YYYY-MM-DD HH:mm:ss',
                  }}
                />
                <ProFormText
                  name="remark"
                  label={
                    <text className="font-semibold">
                      {intl.formatMessage({ id: 'purchase.external.label.remarks' })}
                    </text>
                  }
                  width={'md'}
                  placeholder={intl.formatMessage({ id: 'purchase.external.placeholder.remarks' })}
                  fieldProps={{ count: { max: 100, show: true }, maxLength: 100 }}
                  rules={[{ max: 100 }]}
                  disabled={isEmpty(orderId)}
                />
              </Flex>
            </ProForm>
          </ConfigProvider>
        </ProCard>
        <ProCard className="mt-[1px]">
          <Flex justify="space-between">
            <Flex
              key="summary"
              gap={24}
              justify="flex-start"
              align="center"
              className="text-[16px] font-semibold text-black/85"
            >
              <span>
                {intl.formatMessage({ id: 'purchase.external.label.totalPurchaseQuantity' })}:
                {detailData?.sumQuantity ?? '-'}
              </span>
              <span>
                {intl.formatMessage({ id: 'purchase.external.label.totalAmount' })}:
                <MoneyText text={detailData?.sumAmount} />
              </span>
              <span>
                {intl.formatMessage({ id: 'purchase.external.label.freight' })}:
                <MoneyText text={detailData?.freightAmount} />
              </span>
              <span>
                GST:
                <MoneyText text={detailData?.totalTaxationAmount} />
              </span>
            </Flex>
            <Space>
              <span className="flex flex-row items-center text-[16px] font-semibold text-black/85">
                {intl.formatMessage({ id: 'purchase.external.label.totalPurchaseAmount' })}：
                <span className="text-[24px] font-medium text-primary">
                  <MoneyText text={detailData?.shouldTotalAmount} />
                </span>
              </span>
            </Space>
          </Flex>
          <Flex justify="flex-end" className="mt-4">
            <Space>
              <Checkbox.Group
                options={detailData?.needApprove ? auditPlainOptions : plainOptions}
                value={checkedList}
                onChange={onCheckboxChange}
              />
              <AuthButton
                key="externalSubmit"
                authority="externalSubmit"
                onClick={clikSubmit}
                disabled={dataSourceCache?.length == 0}
                type="primary"
              >
                {intl.formatMessage({ id: 'purchase.external.button.submitPurchaseOrder' })}
              </AuthButton>
            </Space>
          </Flex>
        </ProCard>
      </Spin>
      <StocksInfoDrawer {...stocksDrawer} onClose={() => setStocksDrawer({})} />
      <SupplierDetailDrawer
        {...supplierDetailDrawer}
        onClose={() => setSupplierDetailDrawer({ open: false })}
      />
    </PageContainer>
  );
};

export default withKeepAlive(PurchaseList);
