import FunProTable from '@/components/common/FunProTable';
import withKeep<PERSON>live from '@/wrappers/withKeepAlive';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { history, useIntl } from '@umijs/max';
import { message, Modal } from 'antd';
import React, { useRef, useState } from 'react';
import type { PurchaseRequestEntity } from '../types/purchaseRequest.entity';
import { PostListTableColumns } from './config/postListTableColumns';
import { cancelPurchaseRequest, querypurchaseRequestList, savePurchaseOrderByRequest } from './services';

const PurchaseRequestList: React.FC = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [loading, setLoading] = useState(false);

  // 生成采购单
  const handleGeneratePurchaseOrder = async (record: PurchaseRequestEntity) => {
    // 同一采购门店、入库仓库、供应商的采购需求单明细，才能合并至一个采购单


    Modal.confirm({
      title: intl.formatMessage({ id: 'purchase.request.list.confirm.generateOrder' }),
      content: intl.formatMessage({ id: 'purchase.request.list.confirm.generateOrder.content' }),
      onOk: async () => {
        try {
          setLoading(true);
          const result = await savePurchaseOrderByRequest({
            purchaseOrderList: [
              {
                id: record.id,
                requestNo: record.requestNo,
                itemSn: record.itemSn,
                itemName: record.itemName,
                num: record.num,
                price: record.price,
                storeId: record.storeId,
                storeName: record.storeName,
                supplierId: record.supplierId,
                supplierName: record.supplierName,
                warehouseId: record.warehouseId,
                remark: record.remark,
              },
            ],
          });

          if (result?.orderNo) {
            message.success(intl.formatMessage({ id: 'purchase.request.list.message.generateSuccess' }));
            // 跳转到采购单编辑页面
            history.push(`/purchase/external?orderNo=${result.orderNo}`);
          }
        } catch (error) {
          message.error(intl.formatMessage({ id: 'purchase.request.list.message.generateError' }));
        } finally {
          setLoading(false);
        }
      },
    });
  };

  const handleCancelPurchaseRequest = async (record: PurchaseRequestEntity) => {
    const result = await cancelPurchaseRequest({ id: record.id });
    if (result) {
      message.success(intl.formatMessage({ id: 'purchase.request.list.message.cancelSuccess' }));
      querypurchaseRequestList({});
    }
  };

  return (
    <PageContainer>
      <FunProTable<PurchaseRequestEntity, any>
        rowKey="id"
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        formRef={formRef}
        requestPage={querypurchaseRequestList}
        columns={PostListTableColumns({
          handleGeneratePurchaseOrder,
          handleCancelPurchaseRequest,
          loading,
          intl,
        })}
      />
    </PageContainer>
  );
};

export default withKeepAlive(PurchaseRequestList);