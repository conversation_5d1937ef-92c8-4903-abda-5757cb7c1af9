import { accountListQuerySimple, queryStoreByAccount } from '@/pages/personnel/user/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import type { ProColumns } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { Button, Popconfirm, Space } from 'antd';
import { PurchaseRequestEntity, PurchaseRequestStatus } from '../../types/purchaseRequest.entity';

export interface PostListTableColumnsProps {
  handleGeneratePurchaseOrder: (record: PurchaseRequestEntity) => void;
  handleCancelPurchaseRequest: (record: PurchaseRequestEntity) => void;
  loading: boolean;
  intl: IntlShape;
}

// 状态选项
const statusOptions = {
  [PurchaseRequestStatus.NEW]: {
    text: '已提交',
    status: 'Processing',
  },
  [PurchaseRequestStatus.PURCHASED]: {
    text: '已确认',
    status: 'Success',
  },
  [PurchaseRequestStatus.CANCELED]: {
    text: '已作废',
    status: 'Error',
  },
};

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<PurchaseRequestEntity>[] => [
  {
    title: props.intl.formatMessage({ id: 'common.column.index' }),
    valueType: 'index',
    width: 50,
    fixed: 'left',
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.requestNo' }),
    dataIndex: 'requestNo',
    key: 'requestNo',
    width: 140,
    search: false,
    order: 10,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.sourceNo' }),
    dataIndex: 'sourceNo',
    key: 'sourceNo',
    width: 140,
    search: true,
    order: 7,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.purchaseNo' }),
    dataIndex: 'purchaseNo',
    key: 'purchaseNo',
    width: 140,
    search: true,
    order: 9,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.storeName' }),
    dataIndex: 'storeName',
    key: 'storeName',
    width: 120,
    formItemProps: {
      name: 'storeId',
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
    order: 9
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.warehouseName' }),
    dataIndex: 'warehouseName',
    key: 'warehouseName',
    width: 120,
    order: 8,
    fieldProps: {
      fieldNames: { label: 'warehouseName', value: 'id' },
    },
    request: () => {
      return warehouseList({}).then((s) => {
        return s?.warehouseSimpleRoList;
      });
    },
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.supplierName' }),
    dataIndex: 'supplierName',
    key: 'supplierName',
    width: 120,
    search: false,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.itemName' }),
    dataIndex: 'itemName',
    key: 'itemName',
    width: 150,
    ellipsis: true,
    search: false,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.itemInfo' }),
    dataIndex: 'keyword',
    key: 'keyword',
    width: 120,
    hideInTable: true,
    order: 8,
    fieldProps: {
      placeholder: props.intl.formatMessage({
        id: 'purchase.request.list.columns.itemInfo.placeholder',
      }),
    },
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.itemSn' }),
    dataIndex: 'itemSn',
    key: 'itemSn',
    width: 120,
    search: false,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.price' }),
    dataIndex: 'price',
    key: 'price',
    width: 100,
    search: false,
    valueType: 'money',
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.num' }),
    dataIndex: 'num',
    key: 'num',
    width: 80,
    search: false,
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.creator' }),
    dataIndex: 'creator',
    key: 'creator',
    width: 100,
    search: true,
    order: 7,
    fieldProps: {
      showSearch: true,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: () => accountListQuerySimple({}),
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.createTime' }),
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
    valueType: 'dateTime',
    fieldProps: {
    },
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.status' }),
    dataIndex: 'status',
    key: 'status',
    width: 100,
    search: true,
    order: 10,
    valueEnum: statusOptions,
    formItemProps: {
      name: 'statuses',
    },
    fieldProps: {
      mode: 'multiple',
      defaultValue: [PurchaseRequestStatus.NEW],
    },
  },
  {
    title: props.intl.formatMessage({ id: 'purchase.request.list.columns.updater' }),
    dataIndex: 'updater',
    key: 'updater',
    width: 100,
    search: false,
  },
  {
    title: props.intl.formatMessage({ id: 'common.column.operation' }),
    key: 'operation',
    search: false,
    width: 120,
    fixed: 'right',
    render: (_, record) => {
      return (
        <Space>
          {record.status === PurchaseRequestStatus.NEW && (
            <Button
              type="link"
              size="small"
              loading={props.loading}
              onClick={() => props.handleGeneratePurchaseOrder(record)}
            >
              {props.intl.formatMessage({ id: 'purchase.request.list.button.generateOrder' })}
            </Button>
          )}
          {record.status === PurchaseRequestStatus.NEW && (
            <Popconfirm
              title={props.intl.formatMessage({ id: 'purchase.request.list.confirm.void' })}
              onConfirm={() => props.handleCancelPurchaseRequest(record)}
            >
              <Button
                type="link"
                size="small"
              >
                {props.intl.formatMessage({ id: 'common.button.volid' })}
              </Button>
            </Popconfirm>
          )}
        </Space>
      );
    },
  },
];
