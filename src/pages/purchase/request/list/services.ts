import { PageResponseDataType } from "@/types/PageResponseDataType";
import { request } from "@/utils/request";
import { PurchaseRequestEntity } from "../types/purchaseRequest.entity";
import { SavePurchaseOrderRequest } from "../types/savePurchaseOrder";

export const querypurchaseRequestList = async (params: Partial<PurchaseRequestEntity>): Promise<PageResponseDataType<PurchaseRequestEntity>> => {
  return request(
    `/ipmspurchase/purchaseRequestDetail/pageQuery`,
    {
      data: params,
    },
  );
};


export const cancelPurchaseRequest = async (params: {
  id: number;
}): Promise<boolean> => {
  return request(
    `/ipmspurchase/purchaseRequestDetail/cancelPurchaseRequest`,
    {
      data: params,
    },
  );
};

// 根据需求单生成采购单
export const savePurchaseOrderByRequest = async (params: SavePurchaseOrderRequest): Promise<{ orderNo: string }> => {
  return request(
    `/ipmspurchase/purchaseRequestDetail/savePurchaseOrderByRequest`,
    {
      data: params,
    },
  );
};
