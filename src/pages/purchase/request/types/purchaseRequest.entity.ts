export enum PurchaseRequestStatus {
  // 已提交
  NEW = 'NEW',
  // 已确认
  PURCHASED = 'PURCHASED',
  // 已作废
  CANCELED = 'CANCELED',
}

export interface PurchaseRequestEntity {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人名称
   */
  creator?: string;
  /**
   * 主键id
   */
  id?: number;
  /**
   * 商品图片
   */
  image?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 零售商id
   */
  memberId?: string;
  /**
   * 数量
   */
  num?: number;
  /**
   * 商品实付单价
   */
  price?: number;
  /**
   * 采购单号
   */
  purchaseNo?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 需求单号
   */
  requestNo?: string;
  /**
   * 来源单号;【ReferenceNo】
   */
  sourceNo?: string;
  /**
   * 需求单状态NEW=待确认PURCHASED=已确认CANCELED=已取消
   */
  status?: PurchaseRequestStatus;
  /**
   * 采购门店id
   */
  storeId?: string;
  /**
   * 采购门店
   */
  storeName?: string;
  /**
   * 供应商id
   */
  supplierId?: string;
  /**
   * 供应商name
   */
  supplierName?: string;
  /**
   * 更新人名称
   */
  updater?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 入库仓id
   */
  warehouseId?: string;
  /**
   * 入库仓名称
   */
  warehouseName?: string;
}
