import { ObjectType } from "@/components/ProFormItem/ProFormObject";



export enum receiveTypeEnum {
  SALE = 1,
  RETURN = 2,
  OTHERS = 3,
}

export interface FinReceivableListEntity {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 应收信息
   */
  finReceivableRoList?: FinReceivableEntity[];
  /**
   * None
   */
  isDelete?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 逾期总金额，单位：分
   */
  totalOverdueAmount?: number;
  /**
   * 逾期总金额，单位：元
   */
  totalOverdueAmountYuan?: number;
  /**
   * 应收总金额(多币种)
   */
  totalRecAmountList?: DatumTotalRecAmountList[];
  /**
   * 应收总金额，单位：分
   */
  totalReceivableAmount?: number;
  /**
   * 应收总金额，单位：元
   */
  totalReceivableAmountYuan?: number;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}


export interface FinReceivableEntity {
  /**
   * 订单完成日期
   */
  billDate?: string;
  /**
   * 买方ID
   */
  buyerId?: string;
  /**
   * 买方名称
   */
  buyerName?: string;
  buyerType?: ObjectType;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 台账类型
   */
  ledgerType?: number;
  /**
   * 订单金额，单位：分
   */
  orderAmount?: number;
  /**
   * 订单金额，单位：元
   */
  orderAmountYuan?: number;
  /**
   * 订单号
   */
  orderNo?: string;
  /**
   * 付款方式
   */
  payType?: number;
  /**
   * 汇率
   */
  rate?: number;
  /**
   * 已收金额，单位：分
   */
  receivedAmount?: number;
  /**
   * 已收金额，单位：元
   */
  receivedAmountYuan?: number;
  /**
   * 应收类型
   */
  receiveType?: string;
  /**
   * 剩余应收金额，单位：分
   */
  remainReceivableAmount?: number;
  /**
   * 剩余应收金额，单位：元
   */
  remainReceivableAmountYuan?: number;
  /**
   * 卖方ID
   */
  sellerId?: string;
  /**
   * 卖方名称
   */
  sellerName?: string;
  /**
   * 逾期状态
   */
  status?: number;
  /**
   * 销售门店id
   */
  storeId?: string;
  /**
   * 销售门店名称
   */
  storeName?: string;
  /**
   * 标签
   */
  tag?: 'Over Due' | 'Due';

  lockTag?: string;
  /**
 * 逾期金额(多币种)
 */
  overdueAmountList?: OverdueAmountList[];
  /**
   * 应收
   */
  totalRecAmountList?: OverdueAmountList[];
}

export interface OverdueAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface FinReceivableRoListTotalRecAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}

export interface DatumTotalRecAmountList {
  /**
   * 币种金额
   */
  amount?: number;
  /**
   * 币种
   */
  currency?: string;
  /**
   * 币种符号
   */
  currencySymbol?: string;
}
