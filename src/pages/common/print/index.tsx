/**
 * 远程打印功能
 * 适用于PC/小程序/PDA
 * 1、在移动端以IFrame的形式内嵌此页面，URL传递打印参数
 * 2、此页面接收到打印参数后在前端拼接打印内容
 * 3、点击打印按钮调用远程机器的C-Lodop插件进行打印，打印机安装在远程机器上
 * 4、纸张大小 A4: 210mm*280mm，二等分: 210mm*140mm, 三等分：210mm*93mm
 * 5、本地开发时需要适用IP访问此页面才会正确加载打印的CSS
 */
import { useTitle } from 'ahooks';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
// @ts-ignore
import useStyle from '@/hooks/useStyle';
import GoodTag, { GoodTagPrintContent } from '@/pages/common/print/compoments/GoodTag';
import LocationTag, { LocationTagPrintContent } from '@/pages/common/print/compoments/LocationTag';
import { PaperType } from '@/pages/system/config/components/Print/types/PaperType';
import { getPrinterTypeLocalStorageName } from '@/pages/system/config/components/Print/types/PrinterType';
import { PrintType } from '@/pages/system/config/components/Print/types/PrintType';
import { SalesFields } from '@/pages/system/config/components/Print/types/SalesFields';
import { ConfigType } from '@/pages/system/config/components/types/ConfigType';
import { querySysPropertyList } from '@/services/systerm';
import { useSearchParams } from '@@/exports';
import CheckStock from './compoments/CheckStock';
import InStock from './compoments/InStock';
import OutStock from './compoments/OutStock';
import Purchase from './compoments/Purchase';
import PurchaseReturn from './compoments/PurchaseReturn';
import Sales from './compoments/Sales';
import SalesReturn from './compoments/SalesReturn';

export interface PrintConfig {
  printIP: string;
  [PrintType.salesOrder]: {
    paperType: PaperType;
    fields: SalesFields;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.salesReturnOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.inStockOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.outStockOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.checkOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.purchaseOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.purchaseReturnOrder]: {
    paperType: PaperType;
    printQrCode: boolean;
    remark?: string;
  };
  [PrintType.goodTag]: {
    paperType: PaperType;
  };
  [PrintType.locationTag]: {
    paperType: PaperType;
  };
}

const Print = () => {
  const LODOP = useRef<any>();
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [printing, setPrinting] = useState<boolean>(false);
  const [printConfig, setPrintConfig] = useState<PrintConfig>();
  const [printList, setPrintList] = useState<string[]>([]);
  const [currentPrintName, setCurrentPrintName] = useState<string>();

  const [printGoodList, setPrintGoodList] = useState<GoodTagPrintContent[]>([]);
  const [printLocationList, setPrintLocationList] = useState<LocationTagPrintContent[]>([]);

  const [searchParams] = useSearchParams();
  const printType = searchParams.get('printType') as PrintType;

  // 当前的打印机配置
  const currentPrintConfig: any = printConfig?.[printType];

  useTitle('打印');
  useStyle('/print.css');

  console.log('printConfig', printConfig);

  /**
   * 查询打印配置
   */
  const queryConfig = () => {
    setLoading(true);
    querySysPropertyList({ type: ConfigType.Print, propDimensions: 'ACCOUNT' })
      .then((result) => {
        if (result?.[0]?.value) {
          const data = JSON.parse(result?.[0]?.value);
          setPrintConfig(data);
        } else {
          alert('请到系统设置配置打印机信息');
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };


  /**
   * 获取打印机名称列表
   */
  const getPrintList = () => {
    const printList = [];
    const printNumber = LODOP.current.GET_PRINTER_COUNT();
    for (let i = 0; i < printNumber; i++) {
      printList.push(LODOP.current.GET_PRINTER_NAME(i));
    }
    console.log('打印机列表', printList);
    setPrintList(printList);

    /**
     * 设置默认打印机
     */
    const defaultPrinterName = localStorage.getItem(getPrinterTypeLocalStorageName(printType));
    if (printList.length === 1) {
      setCurrentPrintName(printList[0]);
    } else if (defaultPrinterName && printList.includes(defaultPrinterName)) {
      setCurrentPrintName(defaultPrinterName);
    }
  };

  useEffect(() => {
    queryConfig();
  }, []);

  return (
    <Spin spinning={loading}>
      <div className="h-screen">
        <div className="mx-auto bg-white" id="print">
          <div style={{ padding: '20px' }}>
            {printType === PrintType.salesOrder && (
              <Sales currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.salesReturnOrder && (
              <SalesReturn currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.outStockOrder && (
              <OutStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.inStockOrder && (
              <InStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.purchaseOrder && (
              <Purchase currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.purchaseReturnOrder && (
              <PurchaseReturn currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.checkOrder && (
              <CheckStock currentPrintConfig={currentPrintConfig} />
            )}
            {printType === PrintType.goodTag && <GoodTag setPrintGoodList={setPrintGoodList} />}
            {printType === PrintType.locationTag && (
              <LocationTag setPrintLocationList={setPrintLocationList} />
            )}
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default Print;
