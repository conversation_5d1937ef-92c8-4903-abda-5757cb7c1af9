export default {
  messageMgr: {
    create: {
      title: 'Add Message',
      group: {
        baseInfo: 'Message Information',
      },
      bannerImage: 'Banner Image',
    },
    detail: {
      title: 'Message Detail',
    },
    column: {
      title: 'Message Title',
      content: 'Message Content',
      status: 'Message Status',
      noticeType: 'Notice Type',
      createTime: 'Create Time',
      sendTime: 'Send Time',
      overdueTime: 'Overdue Time',
      updatePerson: 'Update Person',
    },
    placeholder: {
      sendTime: 'If not set, it will take effect immediately',
      overdueTime: 'If not set, it will not expire automatically',
    },
    status: {
      effective: 'Effective',
      invalid: 'Invalid',
    },
    noticeType: {
      notify: 'Notify',
      popup: 'Popup',
      banner: 'Banner',
    },
  },
  protocol: {
    add: {
      title: 'Add Privacy Agreement',
    },
    edit: {
      title: 'Edit Privacy Agreement',
    },
    column: {
      title: 'Agreement Title',
      showType: 'Display Object',
      url: 'Agreement File',
    },
    showType: {
      employee: 'Employee',
      customer: 'Customer',
    },
    form: {
      title: 'Agreement Title',
      showType: 'Display Object',
      file: 'Agreement File',
      'file.extra': 'Support PDF, Word documents, file size no more than 10MB',
    },
    upload: {
      typeError: 'Only PDF or Word documents are allowed!',
      sizeError: 'File size cannot exceed 10MB!',
      success: 'File uploaded successfully',
      error: 'File upload failed',
    },
  },
  user: {
    list: {
      title: 'User List',
      employeeId: 'Employee ID',
      employeeName: 'Employee Name',
      phone: 'Phone',
      store: 'Store',
      role: 'Role',
      gender: 'Gender',
      email: 'Email',
      category: 'Hire Category',
      position: 'Position',
      hireState: 'Hire State',
      remark: 'Remark',
      mainAccount: 'Main Account',
      addEmployee: 'Add Employee',
      editEmployee: 'Edit Employee',
      createAccountTip:
        "Adding an employee will create a store system login account, the account is the new employee's phone number",
      form: {
        employeeName: 'Employee Name',
        phoneNumber: 'Phone Number',
        storePermission: 'Store Permission',
        employeeRole: 'Employee Role',
        phoneValidation: 'Please enter a valid phone number',
      },
      confirm: {
        enableDisable: 'Confirm {action}?',
      },
      account: {
        notBound: 'Not Bound',
        bound: 'Bound',
      },
    },
  },
  store: {
    list: {
      title: 'Store List',
      storeId: 'Store ID',
      storeName: 'Store Name',
      contactPerson: 'Contact Person',
      contactPhone: 'Phone Number',
      storeAddress: 'Store Address',
      mainStore: 'Main Store',
      addStore: 'Add Store',
      editStore: 'Edit Store',
      form: {
        storeName: 'Store Name',
        contactPerson: 'Contact Person',
        contactPhone: 'Contact Phone',
        storeAddress: 'Store Address',
        detailAddress: 'Detail Address',
        contactPhoneValidation: 'Please enter a valid contact phone number',
      },
      confirm: {
        enableDisable: 'Confirm {action}?',
      },
    },
  },
  role: {
    list: {
      title: 'Role List',
      roleName: 'Role Name',
      type: 'Type',
      description: 'Description',
      addRole: 'Add Role',
      editRole: 'Edit Role',
      form: {
        roleName: 'Role Name',
        roleDescription: 'Role Description',
        basicInfo: 'Basic Information',
        functionPermissions: 'Function Permissions',
        sensitiveData: 'Sensitive Data',
      },
      confirm: {
        enableDisable: 'Confirm {action}?',
      },
      roleType: {
        preset: 'Preset',
        create: 'Custom',
        system: 'System',
      },
    },
  },
  job: {
    list: {
      title: 'Job List',
      importQuery: 'Import Query',
      exportQuery: 'Export Query',
      downloadResultFile: 'Download Result File',
      operation: 'Operation',
      functionModule: 'Function Module',
      taskDescription: 'Task Description',
      readRecordCount: 'Read Record Count',
      importSuccessCount: 'Import Success Count',
      importFailCount: 'Import Fail Count',
      operationTime: 'Operation Time',
      completionTime: 'Completion Time',
      operator: 'Operator',
      importTime: 'Import Time',
      exportTime: 'Export Time',
    },
  },
  message: {
    list: {
      title: 'Message List',
      messageTitle: 'Message Title',
      messageContent: 'Message Content',
      notificationTime: 'Notification Time',
      messageType: 'Message Type',
      status: 'Status',
      operation: 'Operation',
      view: 'View',
      messageDetail: 'Message Detail',
      viewPurchaseOrder: 'View Purchase Order',
      viewReorderRecommendation: 'View Reorder Recommendation',
      createReturnOrder: 'Create Return Order',
      viewDetail: 'View Detail',
    },
    bizType: {
      system: 'System Message',
    },
    status: {
      noRead: 'Unread',
      read: 'Read',
    },
    channel: {
      pc: 'PC',
      mp: 'Mini Program',
    },
    jumpType: {
      etcPurchaseOrder: 'ETC Purchase Order',
      etcReturnOrder: 'ETC Return Order',
      reorderRecommendation: 'Reorder Recommendation',
      etcReturnLogistics: 'ETC After-sales Logistics',
    },
    noticeType: {
      message: 'Internal Message',
      notice: 'Notice',
      modal: 'Modal',
    },
  },
  config: {
    tabs: {
      systemSettings: 'System Settings',
      printSettings: 'Print Settings',
    },
    system: {
      purchase: 'Purchase',
      externalPurchaseAudit: 'External Purchase Order Audit Required',
      externalPurchaseAuditDesc:
        'When enabled, external purchase orders must be approved before submission',
      purchaseInSyncGood: 'Record Supplier-Product Association After Purchase Receipt',
      purchaseInSyncGoodDesc:
        'When enabled, the system automatically records supplier-product associations after purchase receipt, which can be viewed on the product management page',
      settingFailedEmpty: 'Setting failed, pricing method cannot be empty!',
      priceRuleError:
        'When "Whether to Quote Price" is set to No, pricing method cannot select link pricing!',
      saveSuccess: 'Save Successful',

      sale: 'Sale',
      checkSaleLowPrice: 'Sale Order Low Price Needs to be Reviewed',
      checkSaleLowPriceDesc: 'When enabled, sale orders must be approved before submission',

      personnel: 'Personnel',
      leaveStartCountDate: 'Leave, Sick, and Long-Term Service Leave System Start Counting Time',
      leaveStartCountDateDesc:
        'The system will count from this time every day, and the recommended setting is to switch the full business to the new system at the time of switching to the new system, and the setting cannot be modified after setting',

      ecommerce: 'Ecommerce',
      ecommerceBackground: 'Ecommerce Background Image',
    },
    print: {
      salesOrderSettings: 'Sales Order Settings',
      salesReturnOrderSettings: 'Sales Return Order Settings',
      outStockOrder: 'Outbound Order',
      inStockOrder: 'Inbound Order',
      checkOrder: 'Inventory Check Order',
      purchaseOrder: 'Purchase Order',
      purchaseReturnOrder: 'Purchase Return Order',
      paperType: 'Paper Type',
      productFieldSettings: 'Product Field Settings',
      printQrCode: 'Print QR Code',
      printRemark: 'Print Remark',
      save: 'Save',
      saveSuccess: 'Save Successful',
    },
    paperType: {
      full: 'A4',
      half: 'Half',
      third: 'One Third',
    },
    salesFields: {
      itemSn: 'Product Code',
      itemName: 'Product Name',
      oeNo: 'OE',
      brandPartNo: 'Brand Part No',
      brandName: 'Brand',
      originRegionName: 'Origin',
      spec: 'Specification',
      adaptModel: 'Vehicle Model',
      saleNum: 'Quantity',
      unitName: 'Unit',
      unitPriceYuan: 'Unit Price',
      actualSellingTotalAmountYuan: 'Unit Price',
      locationCode: 'Location',
    },
  },
  'todo.title': 'Todo List',
  'todo.create': 'Create Todo',
  'todo.edit': 'Edit Todo',
  'todo.cancel': 'Cancel Todo',
  'todo.complete': 'Complete Todo',
  'todo.button.complete': 'Complete',
  'todo.taskDesc': 'Task Description',
  'todo.taskDesc.optional': 'Task Description(Optional)',
  'todo.creator': 'Creator',
  'todo.todoPerson': 'Todo Person',
  'todo.status': 'Status',
  'todo.createTime': 'Create Time',
  'todo.completionTime': 'Completion Time',
  'todo.completionDesc': 'Completion Description',
  'todo.status.notCompleted': 'Not Completed',
  'todo.status.completed': 'Completed',
  'todo.status.cancelled': 'Cancelled',
};
