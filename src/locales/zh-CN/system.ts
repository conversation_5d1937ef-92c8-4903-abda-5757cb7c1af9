export default {
  messageMgr: {
    create: {
      title: '新增消息',
      group: {
        baseInfo: '消息信息',
      },
      bannerImage: 'Banner图片',
    },
    detail: {
      title: '消息详情',
    },
    column: {
      title: '消息标题',
      content: '消息内容',
      status: '消息状态',
      noticeType: '通知形式',
      createTime: '创建时间',
      sendTime: '生效时间',
      overdueTime: '失效时间',
      updatePerson: '更新人',
    },
    placeholder: {
      sendTime: '未设置则立即生效',
      overdueTime: '未设置则不会自动失效',
    },
    status: {
      effective: '生效',
      invalid: '作废',
    },
    noticeType: {
      notify: '普通通知',
      popup: '弹窗通知',
      banner: 'Banner通知',
    },
  },
  protocol: {
    add: {
      title: '新增协议',
    },
    edit: {
      title: '编辑协议',
    },
    column: {
      title: '协议标题',
      showType: '协议对象',
      url: '协议文件',
    },
    showType: {
      employee: '员工',
      customer: '客户',
    },
    form: {
      title: '协议标题',
      showType: '协议对象',
      file: '协议文件',
      'file.extra': '支持上传PDF，文件大小不超过10MB',
    },
    upload: {
      typeError: '只能上传PDF！',
      sizeError: '文件大小不能超过10MB！',
      success: '文件上传成功',
      error: '文件上传失败',
    },
  },
  user: {
    list: {
      title: '用户列表',
      employeeId: '员工ID',
      employeeName: '员工姓名',
      phone: '手机号',
      store: '门店',
      role: '角色',
      gender: '性别',
      email: '邮箱',
      category: '雇佣类型',
      position: '职位',
      hireState: '雇佣状态',
      remark: '备注',
      mainAccount: '主账号',
      addEmployee: '新增员工',
      editEmployee: '编辑员工',
      viewEmployee: '员工详情',
      createAccountTip: '新增员工会创建门店系统登录账号，账号为新员工手机号码',
      form: {
        employeeName: '员工姓名',
        phoneNumber: '手机号码',
        storePermission: '门店权限',
        employeeRole: '员工角色',
        phoneValidation: '请输入正确的手机号码',
      },
      confirm: {
        enableDisable: '确认{action}吗',
      },
      account: {
        notBound: '未绑定',
        bound: '已绑定',
      },
    },
  },
  store: {
    list: {
      title: '门店列表',
      storeId: '门店ID',
      storeName: '门店名称',
      contactPerson: '联系人',
      contactPhone: '手机号码',
      storeAddress: '门店地址',
      mainStore: '主门店',
      addStore: '新增门店',
      editStore: '编辑门店',
      form: {
        storeName: '门店名称',
        contactPerson: '联系人',
        contactPhone: '联系人号码',
        storeAddress: '门店地址',
        detailAddress: '详细地址',
        contactPhoneValidation: '请输入正确的联系人号码',
      },
      confirm: {
        enableDisable: '确认{action}吗',
      },
    },
  },
  role: {
    list: {
      title: '角色列表',
      roleName: '角色名称',
      type: '类型',
      description: '角色描述',
      addRole: '新增角色',
      editRole: '编辑角色',
      form: {
        roleName: '角色名称',
        roleDescription: '角色描述',
        basicInfo: '基础信息',
        functionPermissions: '功能权限',
        sensitiveData: '敏感数据',
      },
      confirm: {
        enableDisable: '确认{action}吗',
      },
      roleType: {
        preset: '预置',
        create: '自建',
        system: '系统',
      },
    },
  },
  job: {
    list: {
      title: '任务列表',
      importQuery: '导入查询',
      exportQuery: '导出查询',
      downloadResultFile: '下载结果文件',
      operation: '操作',
      functionModule: '功能模块',
      taskDescription: '任务描述',
      readRecordCount: '已读取记录数',
      importSuccessCount: '导入成功数',
      importFailCount: '导入失败数',
      operationTime: '操作时间',
      completionTime: '完成时间',
      operator: '操作人',
      importTime: '导入时间',
      exportTime: '导出时间',
    },
  },
  message: {
    list: {
      title: '消息列表',
      messageTitle: '消息标题',
      messageContent: '消息内容',
      notificationTime: '通知时间',
      messageType: '消息类型',
      status: '状态',
      operation: '操作',
      view: '查看',
      messageDetail: '消息详情',
      viewPurchaseOrder: '查看采购单',
      viewReorderRecommendation: '查看补货建议',
      createReturnOrder: '创建采购退货单',
      viewDetail: '查看详情',
    },
    bizType: {
      system: '系统消息',
    },
    status: {
      noRead: '未读',
      read: '已读',
    },
    channel: {
      pc: 'PC',
      mp: '小程序',
    },
    jumpType: {
      etcPurchaseOrder: '一体系采购单',
      etcReturnOrder: '一体系采购退货单',
      reorderRecommendation: '补货建议',
      etcReturnLogistics: '一体系售后物流',
    },
    noticeType: {
      message: '站内信',
      notice: '通知',
      modal: '弹窗',
    },
  },
  config: {
    tabs: {
      systemSettings: '系统设置',
      printSettings: '打印设置',
    },
    system: {
      purchase: '采购',
      externalPurchaseAudit: '采购单是否需要审核',
      externalPurchaseAuditDesc: '开启后采购单审核通过后才能提交',
      purchaseInSyncGood: '采购入库后记录供应商与商品关联',
      purchaseInSyncGoodDesc:
        '开启后采购入库后，系统自动记录供应商与商品关联，关联关系可在商品管理页面查看',
      settingFailedEmpty: '设置失败，报价方式不能为空!',
      priceRuleError: '【是否报价格】选择否时，报价方式不能选择链接报价!',
      saveSuccess: '保存成功',

      sale: '销售',
      checkSaleLowPrice: '销售开单低于最低售价需要审核',
      checkSaleLowPriceDesc: '开启后销售开单审核通过才能正式提交',

      personnel: '人事',
      leaveStartCountDate: '年假、病假、长期服务假系统开始计算时间',
      leaveStartCountDateDesc:
        '系统将从该时间起每天累计假期，建议设置为全量业务切换至新系统的时间，设置后不可修改',

      ecommerce: '商城',
      ecommerceBackground: '商城背景图片',
    },
    print: {
      salesOrderSettings: '销售单设置',
      salesReturnOrderSettings: '销售退货单设置',
      outStockOrder: '出库单',
      inStockOrder: '入库单',
      checkOrder: '盘点单',
      purchaseOrder: '采购单',
      purchaseReturnOrder: '采购退货单',
      paperType: '纸张类型',
      productFieldSettings: '商品字段设置',
      printQrCode: '打印二维码',
      printRemark: '打印备注',
      save: '保存',
      saveSuccess: '保存成功',
    },
    paperType: {
      full: 'A4',
      half: '二分之一',
      third: '三分之一',
    },
    salesFields: {
      itemSn: '商品编码',
      itemName: '商品名称',
      oeNo: 'OE',
      brandPartNo: '供应商编码',
      brandName: '品牌',
      originRegionName: '产地',
      spec: '规格',
      adaptModel: '车型',
      saleNum: '数量',
      unitName: '单位',
      unitPriceYuan: '单价',
      actualSellingTotalAmountYuan: '单价',
      locationCode: '库位',
    },
  },
  'todo.title': '待办列表',
  'todo.create': '新增待办',
  'todo.edit': '编辑待办',
  'todo.cancel': '取消待办',
  'todo.complete': '完成待办',
  'todo.button.complete': '完成',
  'todo.taskDesc': '任务描述',
  'todo.taskDesc.optional': '任务描述(选填)',
  'todo.creator': '创建人',
  'todo.todoPerson': '待办人',
  'todo.status': '任务状态',
  'todo.createTime': '创建时间',
  'todo.completionTime': '完成时间',
  'todo.completionDesc': '完成说明',
  'todo.status.notCompleted': '未完成',
  'todo.status.completed': '已完成',
  'todo.status.cancelled': '已取消',
};
