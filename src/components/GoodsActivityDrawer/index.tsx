import { Drawer, DrawerProps, Tag } from 'antd';
import { useEffect, useState } from 'react';
import {
  ActivityItemEntity,
  ActivityList,
} from '@/components/GoodsActivityDrawer/types/activity.item.entity';
import { ActivityType } from '@/pages/shop/types/ActivityEnum';
import { cn } from '@/lib/utils';
import { GiftRuleType } from '@/pages/shop/types/ActivityDataType';
import { autoLogin, queryActivityItemList } from '@/components/GoodsActivityDrawer/services';
import { RightOutlined } from '@ant-design/icons';

export interface GoodsActivityDrawerProps extends DrawerProps {
  itemId?: string;
  itemName?: string;
  cstId?: string;
}

export default function GoodsActivityDrawer(props: GoodsActivityDrawerProps) {
  const { itemId, itemName, cstId, ...rest } = props;
  const [activityDetail, setActivityDetail] = useState<ActivityItemEntity>();
  const [activityId, setActivityId] = useState<number>();
  const [dSessionId, setDSessionId] = useState<string>();

  useEffect(() => {
    if (itemId && props.visible && cstId) {
      queryActivityItemList({ itemIds: [itemId], cstId }).then((res) =>
        // @ts-ignore
        setActivityDetail(res?.[0] ?? []),
      );
      autoLogin(cstId).then((res) => {
        // @ts-ignore
        setDSessionId(res);
      });
    }
    return () => {
      setActivityId(undefined);
      setDSessionId(undefined);
      setActivityDetail(undefined);
    };
  }, [itemId, props.visible, cstId]);

  const GiftTag = () => {
    return (
      <span className="border rounded-full border-solid border-[#E38780] text-[#D1382B] text-[12px] p-[1px]">
        赠
      </span>
    );
  };

  const getTranslationKeyByRule = (
    giftRuleType: GiftRuleType,
    amount: string | number,
    count: number,
  ) => {
    return giftRuleType === GiftRuleType.FULL_AMOUNT
      ? `购满${amount}，获${count}件赠品`
      : `"购满${amount}件，获${count}件赠品",`;
  };

  /**
   * 获取活动名称(有商品列表)
   * @param activityList
   */
  const getNameFull = (activityList: ActivityList) => {
    switch (activityList.activityType) {
      case ActivityType.BUY_GIFT_SELF:
      case ActivityType.SPECIAL_PRICE:
        return (
          <div className="flex flex-col gap-1 text-[#666] empty:hidden mt-3">
            {activityList?.fullGiveRules?.[0]?.activityGiftItems?.map((item) => (
              <div key={item.itemId}>
                <GiftTag /> {item.itemName} x{item.giftNum!}
              </div>
            ))}
          </div>
        );
      case ActivityType.SUITE_ITEM:
        return (
          <div className="flex flex-col gap-1 text-[#666] empty:hidden mt-3">
            {activityList?.activityItems?.map((item) => (
              <div key={item.itemId}>
                <GiftTag /> {item.itemName} x{item.num!}
              </div>
            ))}
          </div>
        );
      case ActivityType.LADDER_FULL_GIFT:
        return activityList?.fullGiveRules?.map((item, index) => (
          <div key={item.activityId}>
            <div className="mt-3">
              {getTranslationKeyByRule(
                item.giftRuleType!,
                item.enableAmount!,
                item.activityGiftItems?.length!,
              )}
            </div>
            <div className="flex flex-col gap-1 text-[#666] mt-1">
              {item.activityGiftItems?.map((n) => (
                <div key={n.itemId}>
                  <GiftTag /> {n.itemName} x{n.giftNum!}
                </div>
              ))}
            </div>
          </div>
        ));
      case ActivityType.EVERY_FULL_GIFT:
        return (
          <div>
            <div>
              {getTranslationKeyByRule(
                activityList?.fullGiveRules?.[0]?.giftRuleType!,
                activityList?.fullGiveRules?.[0]?.enableAmount!,
                activityList?.fullGiveRules?.[0]?.activityGiftItems?.length!,
              )}
            </div>
            <div className="flex flex-col gap-1 text-[#666] mt-3">
              {activityList.fullGiveRules?.[0]?.activityGiftItems?.map((n) => (
                <div key={n.itemId}>
                  <GiftTag /> {n.itemName} x{n.giftNum!}
                </div>
              ))}
            </div>
          </div>
        );
    }
  };

  return (
    <Drawer
      title={itemName}
      classNames={{
        body: 'bg-gray-100 !p-0',
      }}
      width={activityId ? 1000 : 500}
      {...rest}
    >
      <div className="flex gap-[24px] p-[24px] h-full">
        <div className="flex-1 bg-white p-[24px] rounded-xl flex flex-col gap-2 min-h-0 overflow-y-auto">
          {activityDetail?.activityList?.map((item) => (
            <div
              key={item.activityId}
              className={cn(
                'transition-all py-3 px-4 my-1 cursor-pointer hover:bg-gray-50 border border-solid border-[#140D0D0D] rounded',
                {
                  'border-primary': activityId === item.activityId,
                },
              )}
              onClick={() => {
                setActivityId(item.activityId);
              }}
            >
              <div className="flex flex-col">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Tag color="red">{item.activityTag}</Tag>
                    <div className="text-[16px] font-medium">{item.activityName}</div>
                  </div>
                  <div className="text-primary flex items-center gap-1">
                    活动下单
                    <RightOutlined />
                  </div>
                </div>
                <div className="leading-[1.6]">{getNameFull(item)}</div>
              </div>
            </div>
          ))}
        </div>
        {activityId && (
          <div className="flex-1 bg-white p-[0px] overflow-hidden rounded-xl">
            <iframe
              width={'100%'}
              height={'100%'}
              className="border-none"
              src={`http://localhost:3000/activity/${activityId}?itemId=${
                activityDetail!.itemId
              }&dSessionId=${dSessionId}`}
            />
          </div>
        )}
      </div>
    </Drawer>
  );
}
