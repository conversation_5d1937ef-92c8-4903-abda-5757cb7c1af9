export interface StoreGoodsEntity {
  /**
   * 适用车型
   */
  adaptModel?: string;
  /**
   * etc适配车型id列表
   */
  adaptModelIds?: string[];
  /**
   * 适配车系
   */
  adaptSeries?: string;
  /**
   * 适配车系code
   */
  adaptSeriesCode?: string;
  /**
   * 商品成本价(总)
   */
  allCostPrice?: number;
  origPriceYuan: string;
  /**
   * 可用库存
   */
  avaNum?: number;
  /**
   * 商品条形码
   */
  barCode?: string;
  /**
   * 品牌ID
   */
  brandId?: string;
  /**
   * 品牌
   */
  brandName?: string;
  /**
   * 品牌件号列表
   */
  brandPartNos?: string[];
  /**
   * 品牌简称
   */
  brandShortName?: string;
  /**
   * 类目ID
   */
  categoryId?: string;
  /**
   * 三级类目名称
   */
  categoryName?: string;
  /**
   * 商品成本价
   */
  costPrice?: number;
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * etc有无活动：0-无活动1-有活动
   */
  etcHasActivity?: number;
  /**
   * etc有无库存：0-无货1-有货
   */
  etcHasInventory?: number;
  /**
   * ETC号
   */
  etcNo?: string;
  /**
   * ETC侧采购价格
   */
  etcPurchasePrice?: number;
  /**
   * 图片列表
   */
  images?: string[];
  /**
   * 总库存，账面库存
   */
  inventoryNum?: number;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 是否套装：0-否1-是
   */
  isSuit?: number;
  /**
   * 通用组信息列表
   */
  itemGroupDetails?: ItemGroupDetail[];
  /**
   * 通用组id列表
   */
  itemGroupIdList?: string[];
  /**
   * 通用组列表
   */
  itemGroupRoList?: ItemGroupRoList[];
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 商品名称
   */
  itemName?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 状态:0-禁用1-启用
   */
  itemStatus?: number;
  /**
   * 标签信息列表
   */
  itemTagList?: ItemTagList[];
  /**
   * 1-标准2-非标
   */
  itemType?: number;
  /**
   * 客户上次售价
   */
  lastSalePrice?: number;
  /**
   * 商品库位
   */
  locationCode?: string;
  /**
   * 总结库存
   */
  lockedNum?: number;
  /**
   * 库存下线线
   */
  lowerLimit?: number;
  /**
   * 最低限价
   */
  lowPrice?: number;
  /**
   * 成本价
   */
  costPriceYuan?: number;
  /**
   * 采购库存上限
   */
  maxPurchaseStock?: number;
  /**
   * None
   */
  memberId?: string;
  /**
   * 助记码
   */
  memCode?: string;
  /**
   * 最小起订量
   */
  minOrderNum?: number;
  /**
   * 最小包装量
   */
  minPackNum?: number;
  /**
   * 采购库存下限
   */
  minPurchaseStock?: number;
  /**
   * OE列表
   */
  oeNos?: string[];
  /**
   * 订货编码
   */
  orderCode?: string;
  /**
   * 产地id
   */
  originRegionId?: string;
  /**
   * 产地
   */
  originRegionName?: string;
  /**
   * 自编码
   */
  ownCode?: string;
  /**
   * 定价详情
   */
  priceDetails?: PriceDetail[];
  /**
   * 采购价
   */
  purchasePrice?: number;
  /**
   * 采购状态:0-否1-是
   */
  purchaseStatus?: number;
  /**
   * 真实品牌
   */
  realBrandName?: string;
  /**
   * 真实供应商编码
   */
  realBrandPartNo?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 日均销量
   */
  saleAvgNum?: number;
  /**
   * 30天销量
   */
  saleNum30d?: number;
  /**
   * 销售状态:0-否1-是
   */
  saleStatus?: number;
  /**
   * skuId
   */
  skuId?: string;
  /**
   * SKU商品名称
   */
  skuName?: string;
  /**
   * 商品备注
   */
  skuRemark?: string;
  /**
   * 三方数据编码
   */
  sourceCode?: string;
  /**
   * 商品规格
   */
  spec?: string;
  /**
   * 建议售价
   */
  suggestPrice?: number;
  /**
   * 供应商列表
   */
  supplierList?: SupplierList[];
  /**
   * 三方商品号
   */
  thirdNo?: string;
  /**
   * 可用库存（零售商下所有库存）
   */
  totalAvaNum?: number;
  /**
   * 账面库存（零售商下所有库存）
   */
  totalInventoryNum?: number;
  /**
   * 在途库存
   */
  transitNum?: number;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位
   */
  unitName?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
  /**
   * 库存上线
   */
  upperLimit?: number;
  /**
   * 仓库ID
   */
  warehouseId?: number;
  /**
   * 活动标签
   */
  activityTag?: string;
}

export interface ItemGroupDetail {
  /**
   * None
   */
  createPerson?: string;
  /**
   * None
   */
  createTime?: string;
  /**
   * 通用组id
   */
  groupId?: string;
  /**
   * 明细detailId
   */
  id?: string;
  /**
   * None
   */
  isDelete?: number;
  /**
   * 是否为主商品，0:否，1:是
   */
  isMain?: number;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * None
   */
  memberId?: string;
  /**
   * 通用组名称
   */
  name?: string;
  /**
   * None
   */
  updatePerson?: string;
  /**
   * None
   */
  updateTime?: string;
}

export interface ItemGroupRoList {
  /**
   * 主键id
   */
  id?: number;
  /**
   * 通用组名称
   */
  name?: string;
  /**
   * 0禁用1启用
   */
  state?: number;
  /**
   * 调拨建议按主商品合并，0:否，1是
   */
  transferSuggest?: number;
}

export interface ItemTagList {
  /**
   * param
   */
  createPerson?: string;
  /**
   * param
   */
  createTime?: string;
  /**
   * param
   */
  isDelete?: number;
  /**
   * param
   */
  memberId?: string;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlag?: number;
  /**
   * 0:不对客显示,1:对客显示
   */
  showFlagName?: string;
  /**
   * 标签id
   */
  tagId?: string;
  /**
   * 标签名称
   */
  tagName?: string;
  /**
   * 0=启用1=禁用
   */
  tagStatus?: number;
  /**
   * 0=启用1=禁用
   */
  tagStatusName?: string;
  /**
   * param
   */
  updatePerson?: string;
  /**
   * param
   */
  updateTime?: string;
}

export interface PriceDetail {
  /**
   * 商品ID
   */
  itemId?: string;
  /**
   * 分层定价id
   */
  levelId?: string;
  /**
   * 分层定价名称
   */
  levelName?: string;
  /**
   * 价格
   */
  levelPrice?: number;
  /**
   * 门店零售商id
   */
  memberId?: string;
}

export interface SupplierList {
  /**
   * 供应商ID
   */
  supplierId?: string;
  /**
   * 供应商名称
   */
  supplierName?: string;
}
